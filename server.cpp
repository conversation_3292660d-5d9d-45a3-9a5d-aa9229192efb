#include <stdio.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <string.h>

int main() {
	WSADATA wsaData;
	SOCKET serverSocket;
	struct sockaddr_storage clientAddr;
	char buffer[1024];
	int clientAddrSize = sizeof(clientAddr);
	char clientIP[INET_ADDRSTRLEN];
	if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
		printf("初始化Winsock失败。\n");
		return 1;
	}
	
	serverSocket = socket(AF_INET, SOCK_DGRAM, 0);
	if (serverSocket == INVALID_SOCKET) {
		printf("创建套接字失败。\n");
		WSACleanup();
		return 1;
	}
	
	struct sockaddr_in serverAddr = {0};
	serverAddr.sin_family = AF_INET;
	serverAddr.sin_addr.s_addr = inet_addr("127.0.0.1");
	serverAddr.sin_port = htons(1878);
	
	if (bind(serverSocket, (struct sockaddr *)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
		printf("绑定失败。\n");
		closesocket(serverSocket);
		WSACleanup();
		return 1;
	}
	
	printf("服务端已准备好接收消息...\n");
	
	int recvLen = recvfrom(serverSocket, buffer, sizeof(buffer) - 1, 0, (struct sockaddr *)&clientAddr, &clientAddrSize);
	if (recvLen == SOCKET_ERROR) {
		printf("接收消息失败。\n");
	} else {
		buffer[recvLen] = '\0';
		handleAddress(&clientAddr, clientIP, sizeof(clientIP));
		printf("收到来自 %s 的消息: %s\n", clientIP, buffer);
		
		sendto(serverSocket, buffer, recvLen, 0, (struct sockaddr *)&clientAddr, clientAddrSize);
		printf("消息已发送回客户端。\n");
	}
	
	closesocket(serverSocket);
	WSACleanup();
	
	return 0;
}
