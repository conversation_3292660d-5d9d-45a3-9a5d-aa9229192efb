#include <stdio.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <string.h>

int main() {
	WSADATA wsaData;
	SOCKET clientSocket;
	struct sockaddr_storage serverAddr;
	struct addrinfo hints, *res;
	char buffer[1024];
	char serverIP[INET_ADDRSTRLEN];
	
	if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
		printf("初始化Winsock失败。\n");
		return 1;
	}
	
	memset(&hints, 0, sizeof hints);
	hints.ai_family = AF_INET;
	hints.ai_socktype = SOCK_DGRAM;
	
	if (getaddrinfo("localhost", "1878", &hints, &res) != 0) {
		printf("解析地址失败。\n");
		WSACleanup();
		return 1;
	}
	
	clientSocket = socket(res->ai_family, res->ai_socktype, res->ai_protocol);
	if (clientSocket == INVALID_SOCKET) {
		printf("创建套接字失败。\n");
		freeaddrinfo(res);
		WSACleanup();
		return 1;
	}
	
	memcpy(&serverAddr, res->ai_addr, res->ai_addrlen);
	freeaddrinfo(res);
	
	printf("请输入消息: ");
	fgets(buffer, sizeof(buffer), stdin);
	
	int sendLen = sendto(clientSocket, buffer, strlen(buffer), 0, (struct sockaddr *)&serverAddr, sizeof(serverAddr));
	if (sendLen == SOCKET_ERROR) {
		printf("发送消息失败。\n");
		closesocket(clientSocket);
		WSACleanup();
		return 1;
	}
	
	handleAddress(&serverAddr, serverIP, sizeof(serverIP));
	printf("消息已发送至 %s。\n", serverIP);
	
	int recvLen = recvfrom(clientSocket, buffer, sizeof(buffer) - 1, 0, NULL, NULL);
	if (recvLen == SOCKET_ERROR) {
		printf("接收消息失败。\n");
	} else {
		buffer[recvLen] = '\0';
		printf("收到消息: %s\n", buffer);
	}
	
	closesocket(clientSocket);
	WSACleanup();
	return 0;
}
